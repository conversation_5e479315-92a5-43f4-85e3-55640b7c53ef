'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  ArrowLeft,
  AlertCircle,
  Building2,
  Calendar,
  Check,
  Clock,
  ExternalLink,
  Globe,
  Mail,
  Phone,
  Shield,
  UserCheck,
  Users,
  Zap,
} from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { ClinicType } from '@/lib/types';
import { getClinicById } from '@/actions/admin';
import { fetchAuthSession } from 'aws-amplify/auth';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import Link from 'next/link';

export default function ClinicDetailPage() {
  const router = useRouter();
  const params = useParams();
  const clinicId = params.id as string;

  const [clinic, setClinic] = useState<ClinicType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    async function fetchClinicDetails() {
      try {
        setLoading(true);
        setError(null);

        // Get token from Amplify auth session
        const session = await fetchAuthSession();
        const token = session.tokens?.accessToken?.toString() || '';

        const result = await getClinicById(token, clinicId);

        if (result.ok && result.data) {
          setClinic(result.data.data);
        } else {
          setError(result.error || 'Failed to fetch clinic details');
        }
      } catch (err) {
        console.error('Error fetching clinic details:', err);
        setError('An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    }

    if (clinicId) {
      fetchClinicDetails();
    }
  }, [clinicId]);

  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), 'MMMM dd, yyyy');
    } catch {
      return 'Invalid date';
    }
  };

  if (loading) {
    return <ClinicDetailSkeleton />;
  }

  if (error || !clinic) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Clinics
          </Button>
        </div>
        <Card className="border-red-200 bg-red-50 shadow-sm">
          <CardHeader>
            <CardTitle className="text-destructive flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              Error Loading Clinic
            </CardTitle>
            <CardDescription className="text-destructive/90">
              {error || 'Unable to load clinic details'}
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button
              variant="outline"
              onClick={() => router.refresh()}
              className="border-red-200 text-destructive hover:bg-red-100"
            >
              Try Again
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6 pb-10">
      <div className="flex items-center gap-2">
        <Button variant="ghost" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Clinics
        </Button>
      </div>

      {/* Clinic Header */}
      <div className="flex flex-col md:flex-row gap-6 items-start">
        <div className="h-20 w-20 rounded-xl bg-primary/10 flex items-center justify-center text-primary shrink-0">
          <Building2 className="h-10 w-10" />
        </div>
        <div className="space-y-2 flex-1">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold tracking-tight">
              {clinic.clinic_name}
            </h1>
            {clinic.is_active ? (
              <Badge
                variant="outline"
                className="bg-emerald-50 text-emerald-700 border-emerald-200"
              >
                <Check className="mr-1 h-3 w-3" /> Active
              </Badge>
            ) : (
              <Badge
                variant="outline"
                className="bg-amber-50 text-amber-700 border-amber-200"
              >
                <Clock className="mr-1 h-3 w-3" /> Pending
              </Badge>
            )}
          </div>
          <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
            <div className="flex items-center">
              <Mail className="h-4 w-4 mr-1" />
              <a
                href={`mailto:${clinic.clinic_email}`}
                className="hover:text-primary"
              >
                {clinic.clinic_email}
              </a>
            </div>
            <div className="flex items-center">
              <Globe className="h-4 w-4 mr-1" />
              <a
                href={clinic.clinic_website}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-primary flex items-center"
              >
                {clinic.clinic_website.replace(/^https?:\/\//, '')}
                <ExternalLink className="h-3 w-3 ml-1" />
              </a>
            </div>
            {clinic.human_transfer_destination_number && (
              <div className="flex items-center">
                <Phone className="h-4 w-4 mr-1" />
                {clinic.human_transfer_destination_number}
              </div>
            )}
          </div>
        </div>
        <div className="flex flex-col gap-2 sm:flex-row">
          <Link href={`/admin/clinics/${clinicId}/knowledge-base`}>
            <Button size={'lg'} variant="outline">
              Knowledge Base
            </Button>
          </Link>
          <Link href={`/admin/clinics/${clinicId}/voice-agent`}>
            <Button size={'lg'} variant={'secondary'}>
              Voice Agent
            </Button>
          </Link>
          <Link href={`/admin/clinics/${clinicId}/analytics`}>
            <Button size={'lg'}>View Call Analytics</Button>
          </Link>
        </div>
      </div>

      {/* Tabs and Content */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <div className="border-b">
          <TabsList className="h-12 w-full rounded bg-transparent p-0 justify-start">
            <TabsTrigger
              value="overview"
              className="rounded border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
            >
              Overview
            </TabsTrigger>
            <TabsTrigger
              value="staff"
              className="rounded border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
            >
              Staff
            </TabsTrigger>
            <TabsTrigger
              value="calls"
              className="rounded border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
            >
              Call History
            </TabsTrigger>
            <TabsTrigger
              value="analytics"
              className="rounded border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
            >
              Analytics
            </TabsTrigger>
            <TabsTrigger
              value="settings"
              className="rounded border-b-2 border-transparent data-[state=active]:border-primary/80 data-[state=active]:bg-transparent"
            >
              Settings
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="overflow-hidden border border-gray-200 shadow-sm">
              <CardHeader className="bg-gray-50 border-b border-gray-200 px-6">
                <CardTitle className="flex items-center text-lg">
                  <UserCheck className="h-5 w-5 mr-2 text-primary" />
                  Clinic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 p-6">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Clinic Name
                  </h3>
                  <p className="font-medium">{clinic.clinic_name}</p>
                </div>
                <Separator className="bg-gray-100" />
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Contact Email
                  </h3>
                  <p className="font-medium text-blue-600">
                    {clinic.clinic_email}
                  </p>
                </div>
                <Separator className="bg-gray-100" />
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Website
                  </h3>
                  <a
                    href={clinic.clinic_website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:underline flex items-center font-medium"
                  >
                    {clinic.clinic_website}
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                </div>
                {clinic.human_transfer_destination_number && (
                  <>
                    <Separator className="bg-gray-100" />
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-muted-foreground">
                        Transfer Number
                      </h3>
                      <p className="font-medium">
                        {clinic.human_transfer_destination_number}
                      </p>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            <Card className="overflow-hidden border border-gray-200 shadow-sm">
              <CardHeader className="bg-gray-50 border-b border-gray-200 px-6">
                <CardTitle className="flex items-center text-lg">
                  <Shield className="h-5 w-5 mr-2 text-primary" />
                  Account Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 p-6">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Status
                  </h3>
                  <div>
                    {clinic.is_active ? (
                      <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200 border-0 px-3 py-1">
                        <Check className="mr-1 h-3 w-3" /> Active
                      </Badge>
                    ) : (
                      <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200 border-0 px-3 py-1">
                        <Clock className="mr-1 h-3 w-3" /> Pending Activation
                      </Badge>
                    )}
                  </div>
                </div>
                <Separator className="bg-gray-100" />
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Created On
                  </h3>
                  <p className="flex items-center font-medium">
                    <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
                    {formatDate(clinic.created_at)}
                  </p>
                </div>
                <Separator className="bg-gray-100" />
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Last Updated
                  </h3>
                  <p className="flex items-center font-medium">
                    <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
                    {formatDate(clinic.updated_at)}
                  </p>
                </div>
              </CardContent>
            </Card>

            {clinic.crm_details && (
              <Card className="md:col-span-2 overflow-hidden border border-gray-200 shadow-sm">
                <CardHeader className="bg-gray-50 border-b border-gray-200 px-6">
                  <CardTitle className="flex items-center text-lg">
                    <Zap className="h-5 w-5 mr-2 text-primary" />
                    CRM Integration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 p-6">
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium text-muted-foreground">
                      CRM Provider
                    </h3>
                    <p className="font-medium">{clinic.crm_details.name}</p>
                  </div>

                  {clinic.crm_details.custom_fields &&
                    Object.keys(clinic.crm_details.custom_fields).length >
                      0 && (
                      <>
                        <Separator className="bg-gray-100" />
                        <div className="space-y-2">
                          <h3 className="text-sm font-medium text-muted-foreground">
                            Custom Fields
                          </h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {Object.entries(
                              clinic.crm_details.custom_fields,
                            ).map(([key, value]) => (
                              <div
                                key={key}
                                className="bg-gray-50 p-4 rounded-md border border-gray-100"
                              >
                                <h4 className="text-xs uppercase text-muted-foreground mb-1">
                                  {key}
                                </h4>
                                <p className="text-sm font-medium">{value}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      </>
                    )}
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="staff" className="space-y-6">
          <Card className="overflow-hidden border border-gray-200 shadow-sm">
            <CardHeader className="bg-gray-50 border-b border-gray-200 px-6">
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2 text-primary" />
                Clinic Staff
              </CardTitle>
              <CardDescription>
                Manage staff members associated with this clinic
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="flex items-center justify-center py-16">
                <div className="text-center space-y-3">
                  <div className="bg-gray-50 h-20 w-20 rounded-full flex items-center justify-center mx-auto border border-gray-100">
                    <Users className="h-10 w-10 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium">No staff members yet</h3>
                  <p className="text-muted-foreground max-w-md">
                    Staff management is not available in this preview. This
                    feature will be implemented in a future update.
                  </p>
                  <Button variant="outline" className="mt-2">
                    Invite Staff Member
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calls" className="space-y-6">
          <Card className="overflow-hidden border border-gray-200 shadow-sm">
            <CardHeader className="bg-gray-50 border-b border-gray-200 px-6">
              <CardTitle className="flex items-center">
                <Phone className="h-5 w-5 mr-2 text-primary" />
                Call History
              </CardTitle>
              <CardDescription>
                View all calls handled by Smart Reception for this clinic
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="flex items-center justify-center py-16">
                <div className="text-center space-y-3">
                  <div className="bg-gray-50 h-20 w-20 rounded-full flex items-center justify-center mx-auto border border-gray-100">
                    <Phone className="h-10 w-10 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium">No call history yet</h3>
                  <p className="text-muted-foreground max-w-md">
                    Call history will be available once this clinic starts
                    receiving calls through Smart Reception.
                  </p>
                  <Button variant="outline" className="mt-2">
                    View Demo Analytics
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card className="overflow-hidden border border-gray-200 shadow-sm">
            <CardHeader className="bg-gray-50 border-b border-gray-200 px-6">
              <CardTitle className="flex items-center">
                <Zap className="h-5 w-5 mr-2 text-primary" />
                Analytics Dashboard
              </CardTitle>
              <CardDescription>
                View performance metrics and analytics for this clinic
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="flex items-center justify-center py-16">
                <div className="text-center space-y-3">
                  <div className="bg-gray-50 h-20 w-20 rounded-full flex items-center justify-center mx-auto border border-gray-100">
                    <Zap className="h-10 w-10 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium">Analytics coming soon</h3>
                  <p className="text-muted-foreground max-w-md">
                    Advanced analytics will be available in a future update.
                    Stay tuned for detailed insights into call performance.
                  </p>
                  <Button
                    variant="outline"
                    className="mt-2 bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 border-blue-200"
                  >
                    View Demo Analytics
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card className="overflow-hidden border border-gray-200 shadow-sm">
            <CardHeader className="bg-gray-50 border-b border-gray-200 px-6">
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2 text-primary" />
                Clinic Settings
              </CardTitle>
              <CardDescription>
                Configure settings and preferences for this clinic
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="flex items-center justify-center py-16">
                <div className="text-center space-y-3">
                  <div className="bg-gray-50 h-20 w-20 rounded-full flex items-center justify-center mx-auto border border-gray-100">
                    <Shield className="h-10 w-10 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium">Settings coming soon</h3>
                  <p className="text-muted-foreground max-w-md">
                    Advanced settings will be available in a future update. You
                    will be able to configure call handling preferences, working
                    hours, and more.
                  </p>
                  <Button variant="outline" className="mt-2">
                    Edit Basic Information
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Loading skeleton component
function ClinicDetailSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="ghost" disabled>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Clinics
        </Button>
      </div>

      <div className="flex flex-col md:flex-row gap-6 items-start">
        <Skeleton className="h-20 w-20 rounded-xl" />
        <div className="space-y-4 flex-1">
          <Skeleton className="h-9 w-64" />
          <div className="flex flex-wrap gap-4">
            <Skeleton className="h-6 w-40" />
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-6 w-32" />
          </div>
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-10 w-28" />
          <Skeleton className="h-10 w-40" />
        </div>
      </div>

      <div className="border-b">
        <div className="h-12 flex space-x-4">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-20" />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <Skeleton className="h-8 w-40" />
          <div className="space-y-4">
            <Skeleton className="h-24 w-full rounded-md" />
            <Skeleton className="h-24 w-full rounded-md" />
          </div>
        </div>
        <div className="space-y-4">
          <Skeleton className="h-8 w-40" />
          <div className="space-y-4">
            <Skeleton className="h-24 w-full rounded-md" />
            <Skeleton className="h-24 w-full rounded-md" />
          </div>
        </div>
      </div>
    </div>
  );
}
