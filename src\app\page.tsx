'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import LandingPage from '@/components/blocks/LandingPage';
import { fetchAuthSession } from 'aws-amplify/auth';
import { useAuth } from '@/contexts/AuthContext';

const Home = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const { fetchUserDetails } = useAuth();

  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        // Check if we're in a browser environment
        if (typeof window === 'undefined') {
          setLoading(false);
          return;
        }

        // Check if user is authenticated with Amplify
        try {
          const session = await fetchAuthSession();
          if (session && session.tokens?.accessToken) {
            // User is authenticated, fetch details to determine role
            const userDetails = await fetchUserDetails();

            if (userDetails) {
              // Redirect based on user role
              if (userDetails.isAdmin) {
                router.push('/admin/dashboard');
              } else {
                router.push('/dashboard');
              }
              return;
            }
          }

          // If no valid session or user details, show landing page
          setLoading(false);
        } catch (error) {
          // If error fetching session, user is not authenticated
          console.error('Session check error:', error);
          setLoading(false);
        }
      } catch (error) {
        console.error('Authentication check error:', error);
        setLoading(false);
      }
    };

    checkAuthentication();
  }, [router, fetchUserDetails]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return <LandingPage />;
};

export default Home;
