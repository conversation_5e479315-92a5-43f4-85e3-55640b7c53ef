'use client';

import { useEffect, ReactNode } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: ReactNode;
  requireAdmin?: boolean;
}

export default function ProtectedRoute({
  children,
  requireAdmin = false,
}: ProtectedRouteProps) {
  const { isAuthenticated, isAdmin, loading, checkAuth } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const verifyAuth = async () => {
      const isAuthed = await checkAuth();

      if (!isAuthed) {
        // If not authenticated, redirect to login
        router.push('/login');
        return;
      }

      // If admin access is required but user is not admin
      if (requireAdmin && !isAdmin) {
        // If trying to access admin routes, redirect to regular dashboard
        if (pathname.startsWith('/admin')) {
          router.push('/dashboard');
        }
      }
    };

    if (!loading) {
      verifyAuth();
    }
  }, [
    isAuthenticated,
    isAdmin,
    loading,
    pathname,
    router,
    requireAdmin,
    checkAuth,
  ]);

  if (loading) {
    return (
      <div className="flex w-full flex-col items-center justify-center gap-4">
        <div className="flex h-16 w-16 animate-spin items-center justify-center rounded-full border-4 border-transparent border-t-purple-600 text-4xl text-purple-400">
          <div className="flex h-12 w-12 animate-spin items-center justify-center rounded-full border-4 border-transparent border-t-primary text-2xl text-primary"></div>
        </div>
      </div>
    );
  }

  // If authentication check is complete and user is authenticated
  // (and has admin access if required), render the children
  if (isAuthenticated && (!requireAdmin || isAdmin)) {
    return <>{children}</>;
  }

  // Otherwise render nothing while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
    </div>
  );
}
