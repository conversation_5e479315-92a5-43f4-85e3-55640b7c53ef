import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// This function can be marked `async` if using `await` inside
export function middleware(request: NextRequest) {
  // Get the pathname of the request
  const path = request.nextUrl.pathname;

  // Check if the path is a protected route
  const isDashboardRoute = path.startsWith('/dashboard');
  const isAdminRoute = path.startsWith('/admin') && path !== '/admin-login';

  // Get the amplify token from cookies
  // Note: We can't fully validate the token here in middleware
  // The actual validation will happen in the AuthContext
  const amplifyAuthCookie = request.cookies.get(
    'amplify-authenticator-authenticationInfo',
  )?.value;
  const hasAmplifyAuth = !!amplifyAuthCookie;

  // If trying to access dashboard routes
  if (isDashboardRoute && !hasAmplifyAuth) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  // If trying to access admin routes
  // Note: We can't check admin status here in middleware
  // The actual admin check will happen in the ProtectedRoute component
  if (isAdminRoute && !hasAmplifyAuth) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  // If the user is already authenticated and tries to access login page
  if (path === '/login' && hasAmplifyAuth) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  return NextResponse.next();
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    '/dashboard',
    '/dashboard/:path*',
    '/admin/dashboard',
    '/admin/dashboard/:path*',
    '/login',
  ],
};
