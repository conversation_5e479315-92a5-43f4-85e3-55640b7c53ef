/* eslint-disable @next/next/no-img-element */
import {
  Clock,
  MessageSquare,
  ClipboardCheck,
  Star,
  Facebook,
  Twitter,
  Linkedin,
} from 'lucide-react';
import Header from './Header';
import { Button } from '../ui/button';
import Link from 'next/link';

const LandingPage = () => {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />
      <section className="w-full">
        <div className="container mx-auto flex flex-col md:flex-row items-center max-w-7xl min-h-svh h-dvh">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-primary/5 z-0" />
          <div className="md:w-1/2 md:pr-12 relative z-10">
            <h2 className="text-5xl font-bold tracking-tight leading-tight">
              Revolutionize Your{' '}
              <span className="text-primary">Front Desk</span>
            </h2>
            <p className="mt-6 text-xl text-muted-foreground max-w-xl">
              Smart Reception is your 24/7 AI-powered voice assistant that
              answers patient calls, books appointments, answers FAQs, and
              transfers calls when needed—all with empathy and accuracy.
            </p>
            <div className="mt-8 flex flex-col sm:flex-row gap-4">
              <Button asChild size={'lg'}>
                <Link href="/login">Login</Link>
              </Button>
            </div>
            <div className="mt-8 flex items-center">
              <div className="flex -space-x-2">
                {[1, 2, 3, 4].map((i) => (
                  <div
                    key={i}
                    className={`w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center text-xs font-bold hover:scale-105 transition-all`}
                  >
                    <img
                      src={
                        'https://xvatar.vercel.app/api/avatar/' +
                        i +
                        '?rounded=60&size=120'
                      }
                      alt="Logo"
                    />
                  </div>
                ))}
              </div>
              <p className="ml-4 text-sm text-muted-foreground">
                <span className="font-semibold">500+</span> healthcare providers
                trust us
              </p>
            </div>
          </div>
          <div className="md:w-1/2 mt-12 md:mt-0">
            <div className="relative">
              <img
                src={
                  'https://assets.lummi.ai/assets/QmcqqnDFFwgvV4JoQ2VnMw3ECupGVnRGLj5294JqdMxZfj?auto=format&w=1500'
                }
                alt="Smart Reception AI Voice Agent"
                className="w-full rounded-3xl"
              />
            </div>
          </div>
        </div>
      </section>

      <main className="container mx-auto px-4 py-12">
        {/* Features section with icons */}
        <section id="features" className="mt-20">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Powerful Features</h2>
            <p className="text-muted-foreground max-w-xl mx-auto">
              Everything your clinic needs to streamline patient communication
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-card hover:shadow-lg transition-all hover:translate-y-[-5px] text-card-foreground p-8 rounded-2xl shadow-md border border-muted">
              <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mb-4">
                <Clock className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-3">24/7 Availability</h3>
              <p className="text-muted-foreground">
                Never miss a patient call again. Our AI handles scheduling
                around the clock, ensuring your clinic is always accessible.
              </p>
            </div>
            <div className="bg-card hover:shadow-lg transition-all hover:translate-y-[-5px] text-card-foreground p-8 rounded-2xl shadow-md border border-muted">
              <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mb-4">
                <MessageSquare className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-3">
                Human-like Interaction
              </h3>
              <p className="text-muted-foreground">
                Converses naturally using an empathetic tone and Australian
                accent, creating a comfortable experience for patients.
              </p>
            </div>
            <div className="bg-card hover:shadow-lg transition-all hover:translate-y-[-5px] text-card-foreground p-8 rounded-2xl shadow-md border border-muted">
              <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mb-4">
                <ClipboardCheck className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Easy Integration</h3>
              <p className="text-muted-foreground">
                Works seamlessly with your existing CRM and booking systems,
                requiring minimal setup and providing maximum efficiency.
              </p>
            </div>
          </div>
        </section>

        {/* Testimonials section */}
        <section id="testimonials" className="mt-32">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">What Our Clients Say</h2>
            <p className="text-muted-foreground max-w-xl mx-auto">
              Trusted by healthcare professionals across Australia
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-card p-8 rounded-2xl shadow-md border border-muted">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center text-primary font-bold mr-4">
                  JD
                </div>
                <div>
                  <h4 className="font-semibold">Dr. Jane Doe</h4>
                  <p className="text-sm text-muted-foreground">
                    Sydney Medical Center
                  </p>
                </div>
              </div>
              <p className="text-muted-foreground">
                &ldquo;Smart Reception has transformed our clinic&apos;s
                operations. Our staff can focus on in-person patient care while
                the AI efficiently handles all our calls. The accuracy in
                booking appointments is impressive!&rdquo;
              </p>
              <div className="mt-4 flex text-primary">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star key={star} className="h-5 w-5" fill="currentColor" />
                ))}
              </div>
            </div>
            <div className="bg-card p-8 rounded-2xl shadow-md border border-muted">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center text-primary font-bold mr-4">
                  MS
                </div>
                <div>
                  <h4 className="font-semibold">Dr. Michael Smith</h4>
                  <p className="text-sm text-muted-foreground">
                    Melbourne Health Clinic
                  </p>
                </div>
              </div>
              <p className="text-muted-foreground">
                &ldquo;Our patients love the convenience of booking appointments
                24/7. The AI&apos;s natural conversation flow and accent make
                callers feel comfortable. It&apos;s like having a dedicated
                receptionist who never sleeps!&rdquo;
              </p>
              <div className="mt-4 flex text-primary">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star key={star} className="h-5 w-5" fill="currentColor" />
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA section */}
        <section className="mt-32">
          <div className="relative">
            <div className="absolute -inset-4 rounded-3xl bg-gradient-to-r from-primary/20 to-primary/5 blur-lg"></div>
            <div className="relative bg-card border border-primary/10 p-12 rounded-3xl shadow-xl text-center">
              <h2 className="text-3xl font-bold mb-4">
                Ready to Transform Your Clinic&apos;s Front Desk?
              </h2>
              <p className="text-muted-foreground max-w-2xl mx-auto mb-8">
                Join hundreds of healthcare providers who have streamlined their
                patient communication with Smart Reception&apos;s AI voice
                agent.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="mailto:<EMAIL>">
                  <button className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium py-3 px-8 rounded-xl shadow-lg shadow-primary/20 transition-all hover:translate-y-[-2px]">
                    Get Started Today
                  </button>
                </a>
                <button className="bg-background border border-primary/20 hover:border-primary/50 text-foreground font-medium py-3 px-8 rounded-xl transition-all hover:translate-y-[-2px]">
                  Schedule a Live Demo
                </button>
              </div>
            </div>
          </div>
        </section>
      </main>

      <footer className="bg-muted py-16 mt-32 border-t">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
            <div>
              <h3 className="text-lg font-semibold mb-4">Smart Reception</h3>
              <p className="text-sm text-muted-foreground">
                AI-powered voice assistant revolutionizing healthcare
                communication.
              </p>
            </div>
            <div>
              <h4 className="text-sm font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    Features
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    Pricing
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    Case Studies
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-sm font-semibold mb-4">Resources</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    Blog
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    Documentation
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    Support
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-sm font-semibold mb-4">Contact</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>
                  <a
                    href="mailto:<EMAIL>"
                    className="hover:text-primary transition-colors"
                  >
                    <EMAIL>
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-primary transition-colors">
                    1300 SMART (76278)
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-muted-foreground/20 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-muted-foreground">
              &copy; {new Date().getFullYear()} Smart Reception. All rights
              reserved.
            </p>
            <div className="flex space-x-4 mt-4 md:mt-0">
              <a
                href="#"
                title="Facebook"
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                <Facebook size={20} />
              </a>
              <a
                href="#"
                title="Twitter"
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                <Twitter size={20} />
              </a>
              <a
                href="#"
                title="LinkedIn"
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                <Linkedin size={20} />
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
