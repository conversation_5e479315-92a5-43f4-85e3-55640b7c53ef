{"name": "smart-reception-fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky install"}, "dependencies": {"@aws-amplify/ui-react": "^6.11.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "aws-amplify": "^6.14.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "input-otp": "^1.4.2", "lucide-react": "^0.510.0", "next": "15.3.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.6", "@types/node": "^20", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "eslint": "^9", "eslint-config-next": "15.3.2", "husky": "^9.1.7", "lint-staged": "^15.5.2", "prettier": "^3.5.3", "tailwindcss": "^4.1.6", "tw-animate-css": "^1.2.9", "typescript": "^5"}, "lint-staged": {"*.{js,ts}": "eslint --cache --fix", "*.{ts,js,css,md}": "prettier --write"}}