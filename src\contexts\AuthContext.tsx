'use client';

import {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from 'react';
import { useRouter } from 'next/navigation';
import { signOut } from 'aws-amplify/auth';
import { fetchAuthSession } from 'aws-amplify/auth';
import { UserDetails, getUserDetails } from '@/services/userService';

interface AuthContextType {
  isAuthenticated: boolean;
  isAdmin: boolean;
  loading: boolean;
  userDetails: UserDetails | null;
  logout: () => void;
  checkAuth: () => Promise<boolean>;
  fetchUserDetails: () => Promise<UserDetails | null>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
  const router = useRouter();

  // Function to fetch user details from the API
  const fetchUserDetails = async (): Promise<UserDetails | null> => {
    try {
      const session = await fetchAuthSession();
      if (!session.tokens?.accessToken) {
        return null;
      }

      const token = session.tokens.accessToken.toString();
      const details = await getUserDetails(token);

      if (details) {
        setUserDetails(details);
        setIsAdmin(details.isAdmin);
        return details;
      }

      return null;
    } catch (error) {
      console.error('Error fetching user details:', error);
      return null;
    }
  };

  // Function to check authentication status
  const checkAuth = async (): Promise<boolean> => {
    setLoading(true);

    try {
      // Get session from Amplify
      const session = await fetchAuthSession();
      const isSignedIn = session && session.tokens?.accessToken;

      if (isSignedIn) {
        // User is authenticated with Amplify, fetch user details
        const details = await fetchUserDetails();
        setIsAuthenticated(true);

        if (details) {
          setIsAdmin(details.isAdmin);
        }

        return true;
      } else {
        setIsAuthenticated(false);
        setIsAdmin(false);
        setUserDetails(null);
        return false;
      }
    } catch (error) {
      console.error('Auth check error:', error);
      setIsAuthenticated(false);
      setIsAdmin(false);
      setUserDetails(null);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = () => {
    signOut().then(() => {
      // Update state
      setIsAuthenticated(false);
      setIsAdmin(false);
      setUserDetails(null);

      // Redirect to login
      router.push('/login');
    });
  };
  
  useEffect(() => {
    const initAuth = async () => {
      await checkAuth();
    };

    if (typeof window !== 'undefined') {
      initAuth();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isAdmin,
        loading,
        userDetails,
        logout,
        checkAuth,
        fetchUserDetails,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
