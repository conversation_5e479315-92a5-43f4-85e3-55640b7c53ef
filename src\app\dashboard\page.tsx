'use client';

import { useState } from 'react';
import {
  Building2,
  <PERSON><PERSON>lock,
  ChevronRight,
  Clock,
  Phone,
  PhoneCall,
  PhoneMissed,
  Plus,
  Users,
} from 'lucide-react';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { InviteStaffDialog } from '@/components/blocks/invite-staff-dialog';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell,
} from 'recharts';

// Sample data for charts
const callData = [
  { name: '<PERSON>', calls: 12 },
  { name: '<PERSON><PERSON>', calls: 19 },
  { name: 'Wed', calls: 15 },
  { name: 'Thu', calls: 22 },
  { name: 'Fri', calls: 18 },
  { name: '<PERSON><PERSON>', calls: 8 },
  { name: '<PERSON>', calls: 5 },
];

const callTypeData = [
  { name: 'Answered', value: 75 },
  { name: 'Missed', value: 25 },
];

const COLORS = ['oklch(var(--primary))', 'oklch(var(--destructive))'];

const recentCalls = [
  {
    id: 1,
    name: '<PERSON>',
    number: '(*************',
    time: '10:30 AM',
    duration: '4m 12s',
    status: 'answered',
  },
  {
    id: 2,
    name: 'Michael Brown',
    number: '(*************',
    time: '11:15 AM',
    duration: '2m 45s',
    status: 'answered',
  },
  {
    id: 3,
    name: 'Emily <PERSON>',
    number: '(*************',
    time: '12:05 PM',
    duration: '0m 0s',
    status: 'missed',
  },
  {
    id: 4,
    name: 'Robert Wilson',
    number: '(555) 789-0123',
    time: '1:30 PM',
    duration: '5m 20s',
    status: 'answered',
  },
];

export default function DashboardPage() {
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Dashboard</h1>
        <div className="mt-2 sm:mt-0">
          <Button onClick={() => setIsInviteDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Invite Staff
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Calls</CardTitle>
            <Phone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,248</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Avg. Call Duration
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3m 42s</div>
            <p className="text-xs text-muted-foreground">-8% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Staff Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">+2 new this month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Clinics</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">+1 new this month</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="md:col-span-4">
          <CardHeader>
            <CardTitle>Call Analytics</CardTitle>
            <CardDescription>Call volume over the past week</CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={callData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar 
                  dataKey="calls" 
                  fill="oklch(var(--primary))" 
                  radius={[4, 4, 0, 0]} 
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle>Call Types</CardTitle>
            <CardDescription>
              Distribution of answered vs missed calls
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={callTypeData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="oklch(var(--accent))"
                  dataKey="value"
                  label={({ name, percent }) =>
                    `${name} ${(percent * 100).toFixed(0)}%`
                  }
                >
                  {callTypeData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="md:col-span-4">
          <CardHeader>
            <CardTitle>Recent Calls</CardTitle>
            <CardDescription>Your most recent incoming calls</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentCalls.map((call) => (
                <div key={call.id} className="flex items-center">
                  <div
                    className={`flex h-9 w-9 items-center justify-center rounded-full ${
                      call.status === 'answered' 
                        ? 'bg-primary/10' 
                        : 'bg-destructive/10'
                    }`}
                  >
                    {call.status === 'answered' ? (
                      <PhoneCall className="h-5 w-5 text-primary" />
                    ) : (
                      <PhoneMissed className="h-5 w-5 text-destructive" />
                    )}
                  </div>
                  <div className="ml-4 space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {call.name}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {call.number}
                    </p>
                  </div>
                  <div className="ml-auto text-right text-sm">
                    <p className="font-medium">{call.time}</p>
                    <p className="text-muted-foreground">{call.duration}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full">
              View all calls
              <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle>Upcoming Appointments</CardTitle>
            <CardDescription>Your schedule for today</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-center">
                  <div className="flex h-9 w-9 items-center justify-center rounded-full bg-accent/10">
                    <CalendarClock className="h-5 w-5 text-accent" />
                  </div>
                  <div className="ml-4 space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {i === 1
                        ? 'David Miller'
                        : i === 2
                          ? 'Jessica Taylor'
                          : 'Andrew Clark'}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {i === 1
                        ? '2:00 PM - Checkup'
                        : i === 2
                          ? '3:30 PM - Consultation'
                          : '5:00 PM - Follow-up'}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full">
              View full schedule
              <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
      </div>

      <InviteStaffDialog
        open={isInviteDialogOpen}
        onOpenChange={setIsInviteDialogOpen}
      />
    </div>
  );
}