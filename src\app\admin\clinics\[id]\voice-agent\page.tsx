'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { format, fromUnixTime } from 'date-fns';
import {
  AlertCircle,
  Clock,
  Loader2,
  Mic,
  Volume2,
  Settings2,
  Check,
  X,
  Play,
  LanguagesIcon,
  GraduationCap,
  Hash,
  SpellCheck,
  Zap,
  Timer,
  BookText,
} from 'lucide-react';

import { getVoiceAgent } from '@/actions/voice-agent';
import { VoiceAgent } from '@/lib/agent-types';
import { fetchAuthSession } from 'aws-amplify/auth';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Separator } from '@/components/ui/separator';
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function VoiceAgentPage() {
  const { id } = useParams();
  const [voiceAgent, setVoiceAgent] = useState<VoiceAgent | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchVoiceAgent() {
      try {
        setLoading(true);
        setError(null);

        // Get token from Amplify auth session
        const session = await fetchAuthSession();
        const token = session.tokens?.accessToken?.toString() || '';
        const clinicId = Array.isArray(id) ? id[0] : id;

        if (!clinicId) {
          setError('Clinic ID is required');
          setLoading(false);
          return;
        }

        const result = await getVoiceAgent('6822e9604b8b521b826a0528', token);

        if (result.ok && result.data) {
          setVoiceAgent(result.data);
          console.log('Voice agent data:', result.data);
        } else {
          setError(result.error || 'Failed to fetch voice agent data');
        }
      } catch (err) {
        console.error('Error fetching voice agent:', err);
        setError('An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    }

    fetchVoiceAgent();
  }, [id]);

  const formatTimestamp = (timestamp: number) => {
    try {
      const date =
        timestamp > 9999999999
          ? fromUnixTime(timestamp / 1000)
          : fromUnixTime(timestamp);
      return format(date, 'MMM dd, yyyy HH:mm:ss');
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return 'Invalid date';
    }
  };

  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold tracking-tight">
          Voice Agent Configuration
        </h1>
        <Button variant="outline">
          <Settings2 className="mr-2 h-4 w-4" />
          Edit Configuration
        </Button>
      </div>

      {loading ? (
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin mr-2 text-primary" />
              <p>Loading voice agent configuration...</p>
            </div>
          </CardContent>
        </Card>
      ) : error ? (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-2 mt-2"
              onClick={() => window.location.reload()}
            >
              Try Again
            </Button>
          </AlertDescription>
        </Alert>
      ) : voiceAgent ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Mic className="h-5 w-5 mr-2 text-primary" />
                  Agent Overview
                </CardTitle>
                <CardDescription>
                  Basic information about the voice agent
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Agent Name
                      </p>
                      <h3 className="text-lg font-medium">
                        {voiceAgent.data.agent_name}
                      </h3>
                    </div>
                    <div>
                      {voiceAgent.data.is_published ? (
                        <Badge className="bg-emerald-100 text-emerald-700 hover:bg-emerald-100">
                          <Check className="h-3 w-3 mr-1" /> Published
                        </Badge>
                      ) : (
                        <Badge
                          variant="outline"
                          className="bg-amber-50 text-amber-700 border-amber-200"
                        >
                          <Clock className="h-3 w-3 mr-1" /> Draft
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Agent ID
                      </p>
                      <p className="text-sm font-mono">
                        {voiceAgent.data.agent_id}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Version
                      </p>
                      <p className="text-sm">
                        v{voiceAgent.data.version} -{' '}
                        {voiceAgent.data.version_title}
                      </p>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Last Modified
                    </p>
                    <p className="text-sm">
                      {formatTimestamp(
                        voiceAgent.data.last_modification_timestamp,
                      )}
                    </p>
                  </div>

                  <Separator />

                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Response Engine
                    </p>
                    <div className="flex items-center mt-1">
                      <Zap className="h-4 w-4 mr-2 text-indigo-500" />
                      <p>
                        {voiceAgent.data.response_engine.type} (v
                        {voiceAgent.data.response_engine.version})
                      </p>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      LLM ID: {voiceAgent.data.response_engine.llm_id}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Post-Call Analysis Model
                    </p>
                    <div className="flex items-center mt-1">
                      <GraduationCap className="h-4 w-4 mr-2 text-indigo-500" />
                      <p>{voiceAgent.data.post_call_analysis_model}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Volume2 className="h-5 w-5 mr-2 text-primary" />
                  Voice Configuration
                </CardTitle>
                <CardDescription>Voice settings and parameters</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Voice ID
                      </p>
                      <div className="flex items-center mt-1">
                        <p className="text-sm font-mono">
                          {voiceAgent.data.voice_id}
                        </p>
                      </div>
                    </div>
                    <Button size="sm" variant="outline">
                      <Play className="h-3 w-3 mr-1" /> Test Voice
                    </Button>
                  </div>

                  <div>
                    <div className="flex justify-between items-center">
                      <p className="text-sm font-medium text-muted-foreground">
                        Language
                      </p>
                      <Badge variant="outline" className="ml-2">
                        <LanguagesIcon className="h-3 w-3 mr-1" />{' '}
                        {voiceAgent.data.language}
                      </Badge>
                    </div>
                  </div>

                  <Separator />

                  {voiceAgent.data.voice_temperature !== undefined && (
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <p className="text-sm font-medium">Temperature</p>
                        <span className="text-sm">
                          {voiceAgent.data.voice_temperature.toFixed(1)}
                        </span>
                      </div>
                      <Progress
                        value={voiceAgent.data.voice_temperature * 100}
                        className="h-2"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Determines voice variation and expressiveness
                      </p>
                    </div>
                  )}

                  {voiceAgent.data.voice_speed !== undefined && (
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <p className="text-sm font-medium">Speed</p>
                        <span className="text-sm">
                          ×{voiceAgent.data.voice_speed.toFixed(1)}
                        </span>
                      </div>
                      <Progress
                        value={voiceAgent.data.voice_speed * 50}
                        className="h-2"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Controls the pace of speech
                      </p>
                    </div>
                  )}

                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <p className="text-sm font-medium">Volume</p>
                      <span className="text-sm">{voiceAgent.data.volume}%</span>
                    </div>
                    <Progress value={voiceAgent.data.volume} className="h-2" />
                    <p className="text-xs text-muted-foreground mt-1">
                      Adjust the loudness of the voice
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="call-settings" className="w-full">
            <TabsList className="grid w-full max-w-md grid-cols-3">
              <TabsTrigger value="call-settings">Call Settings</TabsTrigger>
              <TabsTrigger value="pronunciation">Pronunciation</TabsTrigger>
              <TabsTrigger value="dtmf">DTMF Options</TabsTrigger>
            </TabsList>

            <TabsContent value="call-settings">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Clock className="h-5 w-5 mr-2 text-primary" />
                    Call Settings
                  </CardTitle>
                  <CardDescription>
                    Configure call behavior and timeouts
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableBody>
                      <TableRow>
                        <TableCell className="font-medium">
                          Maximum Call Duration
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Timer className="h-4 w-4 mr-2 text-muted-foreground" />
                            {formatDuration(
                              voiceAgent.data.max_call_duration_ms,
                            )}
                          </div>
                        </TableCell>
                      </TableRow>

                      <TableRow>
                        <TableCell className="font-medium">
                          Voicemail Detection Timeout
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                            {formatDuration(
                              voiceAgent.data.voicemail_detection_timeout_ms,
                            )}
                          </div>
                        </TableCell>
                      </TableRow>

                      <TableRow>
                        <TableCell className="font-medium">
                          Sensitive Data Storage
                        </TableCell>
                        <TableCell>
                          {voiceAgent.data.opt_out_sensitive_data_storage ? (
                            <Badge
                              variant="outline"
                              className="bg-red-50 text-red-700"
                            >
                              <X className="h-3 w-3 mr-1" /> Opted Out
                            </Badge>
                          ) : (
                            <Badge
                              variant="outline"
                              className="bg-emerald-50 text-emerald-700"
                            >
                              <Check className="h-3 w-3 mr-1" /> Enabled
                            </Badge>
                          )}
                        </TableCell>
                      </TableRow>

                      <TableRow>
                        <TableCell className="font-medium">
                          Signed URL
                        </TableCell>
                        <TableCell>
                          {voiceAgent.data.opt_in_signed_url ? (
                            <Badge
                              variant="outline"
                              className="bg-emerald-50 text-emerald-700"
                            >
                              <Check className="h-3 w-3 mr-1" /> Enabled
                            </Badge>
                          ) : (
                            <Badge
                              variant="outline"
                              className="bg-slate-100 text-slate-700"
                            >
                              <X className="h-3 w-3 mr-1" /> Disabled
                            </Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            {voiceAgent.data.pronunciation_dictionary && (
              <TabsContent value="pronunciation">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <SpellCheck className="h-5 w-5 mr-2 text-primary" />
                      Pronunciation Dictionary
                    </CardTitle>
                    <CardDescription>
                      Custom word pronunciations using IPA phonetics
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {voiceAgent.data.pronunciation_dictionary.length === 0 ? (
                      <div className="text-center py-8">
                        <BookText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-medium">
                          No pronunciation entries
                        </h3>
                        <p className="text-muted-foreground">
                          No custom pronunciations have been defined for this
                          agent.
                        </p>
                      </div>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Word</TableHead>
                            <TableHead>Alphabet</TableHead>
                            <TableHead>Phoneme</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {voiceAgent.data.pronunciation_dictionary.map(
                            (entry, index) => (
                              <TableRow key={index}>
                                <TableCell className="font-medium">
                                  {entry.word}
                                </TableCell>
                                <TableCell>
                                  {entry.alphabet.toUpperCase()}
                                </TableCell>
                                <TableCell className="font-mono">
                                  {entry.phoneme}
                                </TableCell>
                              </TableRow>
                            ),
                          )}
                        </TableBody>
                      </Table>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            )}

            <TabsContent value="dtmf">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Hash className="h-5 w-5 mr-2 text-primary" />
                    DTMF Configuration
                  </CardTitle>
                  <CardDescription>
                    Dual-tone multi-frequency signaling options
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <div className="flex-grow">
                        <p className="font-medium">Allow User DTMF</p>
                        <p className="text-sm text-muted-foreground">
                          Enable touch-tone phone keypad inputs during calls
                        </p>
                      </div>
                      {voiceAgent.data.allow_user_dtmf ? (
                        <Badge
                          variant="outline"
                          className="bg-emerald-50 text-emerald-700"
                        >
                          <Check className="h-3 w-3 mr-1" /> Enabled
                        </Badge>
                      ) : (
                        <Badge
                          variant="outline"
                          className="bg-slate-100 text-slate-700"
                        >
                          <X className="h-3 w-3 mr-1" /> Disabled
                        </Badge>
                      )}
                    </div>

                    {voiceAgent.data.allow_user_dtmf && (
                      <div>
                        <p className="font-medium mb-2">DTMF Options</p>
                        <pre className="bg-slate-50 p-4 rounded-md text-xs overflow-auto">
                          {JSON.stringify(
                            voiceAgent.data.user_dtmf_options,
                            null,
                            2,
                          )}
                        </pre>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      ) : (
        <Alert className="bg-amber-50 text-amber-800 border-amber-200">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>No Voice Agent Found</AlertTitle>
          <AlertDescription>
            No voice agent configuration was found for this clinic. You can
            create a new voice agent configuration.
            <Button
              size="sm"
              className="ml-2 mt-2 bg-amber-600 hover:bg-amber-700 text-white"
            >
              Create Voice Agent
            </Button>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
