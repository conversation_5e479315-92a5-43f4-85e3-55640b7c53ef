'use client';

import { KnowlegdeBaseType } from '@/lib/types';

/**
 * Upload files with progress tracking
 * @param url The endpoint URL to upload to
 * @param formData The FormData containing files to upload
 * @param token Bearer token for authorization
 * @param onProgress Callback function to track upload progress
 * @returns Promise with the response matching the server action response format
 */
export const uploadFilesWithProgress = (
  url: string,
  formData: FormData,
  token: string,
  onProgress?: (percentage: number) => void,
): Promise<{
  ok: boolean;
  status: number;
  data?: KnowlegdeBaseType;
  error?: string;
}> => {
  return new Promise((resolve) => {
    const xhr = new XMLHttpRequest();

    // Track upload progress
    if (onProgress) {
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const percentage = Math.round((event.loaded * 100) / event.total);
          onProgress(percentage);
        }
      });
    }

    // Handle completion
    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText) as KnowlegdeBaseType;
          resolve({
            ok: true,
            status: xhr.status,
            data: response,
          });
        } catch (error) {
          console.error('Error parsing response:', error);
          resolve({
            ok: true,
            status: xhr.status,
            data: { data: xhr.responseText } as unknown as KnowlegdeBaseType,
            error: 'Invalid response format',
          });
        }
      } else {
        let errorMessage = 'Upload failed';
        try {
          const error = JSON.parse(xhr.responseText);
          errorMessage = error.message || error.error || errorMessage;
        } catch (parseError) {
          // Parsing error, use default message
          console.error('Error parsing error response:', parseError);
        }

        resolve({
          ok: false,
          status: xhr.status,
          error: errorMessage,
        });
      }
    });

    // Handle errors
    xhr.addEventListener('error', () => {
      resolve({
        ok: false,
        status: xhr.status || 0,
        error: 'Network error occurred during upload',
      });
    });

    xhr.addEventListener('abort', () => {
      resolve({
        ok: false,
        status: xhr.status || 0,
        error: 'Upload was aborted',
      });
    });

    // Open connection and send data
    xhr.open('POST', url, true);
    xhr.setRequestHeader('Authorization', `Bearer ${token}`);
    // Don't set Content-Type header here, it will be automatically set with the correct boundary
    xhr.send(formData);
  });
};
