// Twilio Phone Numbers Types
export interface TwilioPhoneNumber {
  accountSid: string;
  addressSid: string | null;
  addressRequirements: string;
  apiVersion: string;
  beta: boolean;
  capabilities: {
    fax: boolean;
    mms: boolean;
    sms: boolean;
    voice: boolean;
  };
  dateCreated: string;
  dateUpdated: string;
  friendlyName: string;
  identitySid: string | null;
  phoneNumber: string;
  origin: string;
  sid: string;
  smsApplicationSid: string;
  smsFallbackMethod: string;
  smsFallbackUrl: string;
  smsMethod: string;
  smsUrl: string;
  statusCallback: string;
  statusCallbackMethod: string;
  trunkSid: string | null;
  uri: string;
  voiceReceiveMode: string;
  voiceApplicationSid: string | null;
  voiceCallerIdLookup: boolean;
  voiceFallbackMethod: string;
  voiceFallbackUrl: string;
  voiceMethod: string;
  voiceUrl: string;
  emergencyStatus: string;
  emergencyAddressSid: string | null;
  emergencyAddressStatus: string;
  bundleSid: string | null;
  status: string;
}

export interface TwilioPhoneNumbersResponse {
  data: TwilioPhoneNumber[];
}
