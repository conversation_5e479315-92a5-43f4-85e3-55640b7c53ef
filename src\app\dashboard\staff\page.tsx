'use client';

import { useState } from 'react';
import {
  Check,
  Clock,
  MoreHorizontal,
  Plus,
  Search,
  Shield,
  ShieldAlert,
  User,
} from 'lucide-react';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { InviteStaffDialog } from '@/components/blocks/invite-staff-dialog';

// Sample staff data
const staffMembers = [
  {
    id: 1,
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    role: 'Master Admin',
    status: 'active',
    joinedDate: 'Jan 10, 2023',
  },
  {
    id: 2,
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    role: 'Standard Admin',
    status: 'active',
    joinedDate: 'Feb 15, 2023',
  },
  {
    id: 3,
    name: 'Dr. Michael Brown',
    email: '<EMAIL>',
    role: 'Standard Admin',
    status: 'active',
    joinedDate: 'Mar 22, 2023',
  },
  {
    id: 4,
    name: 'Dr. Emily Davis',
    email: '<EMAIL>',
    role: 'Standard Admin',
    status: 'pending',
    joinedDate: 'Apr 5, 2023',
  },
  {
    id: 5,
    name: 'Dr. Robert Wilson',
    email: '<EMAIL>',
    role: 'Standard Admin',
    status: 'pending',
    joinedDate: 'May 12, 2023',
  },
];

export default function StaffManagementPage() {
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredStaff = staffMembers.filter(
    (staff) =>
      staff.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      staff.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      staff.role.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Staff Management</h1>
        <div className="mt-2 sm:mt-0">
          <Button onClick={() => setIsInviteDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Invite Staff
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Staff Members</CardTitle>
          <CardDescription>
            Manage your clinic staff and their access levels
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4">
            <div className="flex w-full max-w-sm items-center space-x-2">
              <Input
                placeholder="Search staff..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="h-9"
              />
              <Button variant="outline" size="sm" className="h-9 px-4 shrink-0">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </div>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Joined</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredStaff.map((staff) => (
                    <TableRow key={staff.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage
                              src={`https://xvatar.vercel.app/api/avatar/${staff.email}.svg?rounded=120&size=240`}
                            />
                            <AvatarFallback>
                              {staff.name
                                .split(' ')
                                .map((n) => n[0])
                                .join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div>{staff.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {staff.email}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {staff.role === 'Master Admin' ? (
                            <ShieldAlert className="h-4 w-4 text-amber-500" />
                          ) : (
                            <Shield className="h-4 w-4 text-slate-400" />
                          )}
                          {staff.role}
                        </div>
                      </TableCell>
                      <TableCell>
                        {staff.status === 'active' ? (
                          <Badge
                            variant="outline"
                            className="bg-emerald-50 text-emerald-700 border-emerald-200"
                          >
                            <Check className="mr-1 h-3 w-3" /> Active
                          </Badge>
                        ) : (
                          <Badge
                            variant="outline"
                            className="bg-amber-50 text-amber-700 border-amber-200"
                          >
                            <Clock className="mr-1 h-3 w-3" /> Pending
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>{staff.joinedDate}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <User className="mr-2 h-4 w-4" />
                              <span>View Profile</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Shield className="mr-2 h-4 w-4" />
                              <span>Change Role</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {staff.status === 'pending' ? (
                              <DropdownMenuItem>
                                <Clock className="mr-2 h-4 w-4" />
                                <span>Resend Invitation</span>
                              </DropdownMenuItem>
                            ) : null}
                            <DropdownMenuItem className="text-red-600">
                              <span>Remove</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      <InviteStaffDialog
        open={isInviteDialogOpen}
        onOpenChange={setIsInviteDialogOpen}
      />
    </div>
  );
}
