export interface RetellCallTranscriptWord {
  word: string;
  start: number;
  end: number;
}

export interface RetellCallTranscriptObject {
  role: string;
  content: string;
  words: RetellCallTranscriptWord[];
  metadata: {
    response_id: number;
  };
}

export interface RetellCallLatency {
  p99: number;
  min: number;
  max: number;
  p90: number;
  num: number;
  values: number[];
  p50: number;
  p95: number;
}

export interface RetellCallCostProduct {
  unit_price: number;
  product: string;
  cost: number;
}

export interface RetellCallCost {
  total_duration_unit_price: number;
  product_costs: RetellCallCostProduct[];
  combined_cost: number;
  total_duration_seconds: number;
}

export interface RetellCallAnalysis {
  in_voicemail: boolean;
  call_summary: string;
  user_sentiment: string;
  custom_analysis_data: Record<string, unknown>;
  call_successful: boolean;
}

export interface RetellCall {
  call_id: string;
  call_type: string;
  agent_id: string;
  retell_llm_dynamic_variables?: {
    clinic_name?: string;
  };
  call_status: string;
  start_timestamp: number;
  end_timestamp: number;
  duration_ms: number;
  transcript: string;
  transcript_object: RetellCallTranscriptObject[];
  recording_url: string;
  public_log_url: string;
  disconnection_reason: string;
  latency: {
    llm: RetellCallLatency;
    e2e: RetellCallLatency;
    tts: RetellCallLatency;
    knowledge_base: RetellCallLatency;
  };
  call_cost: RetellCallCost;
  call_analysis: RetellCallAnalysis;
  opt_out_sensitive_data_storage: boolean;
  opt_in_signed_url: boolean;
  from_number: string;
  to_number: string;
  direction: string;
  telephony_identifier: {
    twilio_call_sid: string;
  };
  execution_message?: string;
}

export interface RetellCallListResponse {
  data: RetellCall[];
}
