'use server';

import { TwilioPhoneNumbersResponse } from '@/lib/twilio-types';
import { API_ENDPOINTS, QueryParams } from '.';

export async function getAdminClinics(token?: string) {
  try {
    const response = await fetch(API_ENDPOINTS.ADMIN_CLINICS, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token ? `Bearer ${token}` : '',
      },
      next: {
        revalidate: 6000,
        tags: ['clinics'],
      },
    });

    const data = await response.json();

    return {
      ok: response.ok,
      status: response.status,
      data,
    };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to fetch clinics. Please try again.',
    };
  }
}

export async function getClinicById(token?: string, clinicId?: string) {
  if (!clinicId) {
    return {
      ok: false,
      status: 400,
      error: 'Clinic ID is required',
    };
  }

  try {
    const url = API_ENDPOINTS.CLINIC_DETAILS.replace(':clinic_id', clinicId);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token ? `Bearer ${token}` : '',
      },
      next: {
        revalidate: 6000,
        tags: [`clinic-${clinicId}`],
      },
    });

    const data = await response.json();

    return {
      ok: response.ok,
      status: response.status,
      data,
    };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to fetch clinic details. Please try again.',
    };
  }
}

export async function getClinicAnalytics(token: string, clinicId: string) {
  if (!clinicId) {
    return {
      ok: false,
      status: 400,
      error: 'Clinic ID is required',
    };
  }

  try {
    const url = new URL(API_ENDPOINTS.RETELL_ANALYTICS_LIST_CALLS);
    url.searchParams.set(QueryParams.CLINIC_ID, clinicId);

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      next: {
        revalidate: 6000,
        tags: [`clinic-analytics-${clinicId}`],
      },
    });

    const data = await response.json();

    return {
      ok: response.ok,
      status: response.status,
      data,
    };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to fetch clinic analytics. Please try again.',
    };
  }
}

export async function getTwilioPhoneNumbers(token: string) {
  try {
    const response = await fetch(API_ENDPOINTS.ADMIN_TWILIO_PHONE_NUMBERS, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      cache: 'no-store',
    });

    const data = (await response.json()) as TwilioPhoneNumbersResponse;

    return {
      ok: response.ok,
      status: response.status,
      data,
    };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to fetch Twilio phone numbers. Please try again.',
    };
  }
}
