# Check out https://hub.docker.com/_/node to select a new base image
FROM --platform=linux/amd64 node:22-alpine

# create and set app directory
ARG CODE_SOURCE=/home/<USER>/app
RUN mkdir -p $CODE_SOURCE
WORKDIR $CODE_SOURCE

# Bundle app source
COPY . $CODE_SOURCE

# Build this app
RUN yarn install
RUN yarn run build

# Bind to all network interfaces so that it can be mapped to the host OS
ENV HOST=0.0.0.0 PORT=3000

EXPOSE ${PORT}
CMD [ "yarn", "run", "start" ]