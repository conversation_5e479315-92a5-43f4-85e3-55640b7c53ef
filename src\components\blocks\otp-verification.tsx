'use client';

import { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from '@/components/ui/input-otp';

interface OtpVerificationProps {
  onVerified: () => void;
  email: string;
  authFlow: 'SIGNIN' | 'SIGNUP';
  onOtpSubmit: (otp: string) => Promise<void>;
}

export function OtpVerification({
  onVerified,
  email,
  onOtpSubmit,
  authFlow
}: OtpVerificationProps) {
  const [isVerifying, setIsVerifying] = useState(false);

  // OTP length is determined by the auth flow
  const otpLength = authFlow === 'SIGNUP' ? 6 : 8;

  const form = useForm({
    resolver: zodResolver(
      z.object({
        otp: z
          .string()
          .length(otpLength, { message: `OTP must be ${otpLength} digits` }),
      }),
    ),
    defaultValues: {
      otp: '',
    },
  });

  async function onSubmit(values: { otp: string }) {
    try {
      setIsVerifying(true);
      await onOtpSubmit(values.otp);
      onVerified();
    } catch (error) {
      console.error(error);
      toast.error((error as Error).name, {
        description: (error as Error).message,
      });
    } finally {
      setIsVerifying(false);
    }
  }

  return (
    <Card className="w-full max-w-sm py-10">
      <CardHeader className="space-y-2">
        <CardTitle className="text-2xl font-bold">Verify your email</CardTitle>
        <CardDescription>
          We&apos;ve sent a {otpLength}-digit verification code to {email}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex items-center justify-center gap-y-4 mt-2 flex-col"
          >
            <FormField
              control={form.control}
              name="otp"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <InputOTP maxLength={otpLength} {...field}>
                      <InputOTPGroup>
                        {Array.from({ length: otpLength }).map((_, i) => (
                          <InputOTPSlot key={i} index={i} />
                        ))}
                      </InputOTPGroup>
                    </InputOTP>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className="w-64" disabled={isVerifying}>
              {isVerifying ? 'Verifying...' : 'Verify'}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
