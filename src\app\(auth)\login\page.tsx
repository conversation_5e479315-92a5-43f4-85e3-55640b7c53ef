'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { OtpVerification } from '@/components/blocks/otp-verification';
import { toast } from 'sonner';
import {
  confirmSignIn,
  confirmSignUp,
  signIn,
  signUp,
  autoSignIn,
} from 'aws-amplify/auth';
import '@/lib/amplify';
import { useAuth } from '@/contexts/AuthContext';

const formSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

export default function LoginPage() {
  const router = useRouter();
  const { fetchUserDetails } = useAuth();
  const [showOtpVerification, setShowOtpVerification] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [authFlow, setAuthFlow] = useState<'SIGNUP' | 'SIGNIN'>('SIGNIN');

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  });

  // const initiateSignin = async (values: z.infer<typeof formSchema>) => {
  //   try {
  //     const { nextStep } = await signIn({
  //       username: values.email,
  //       options: {
  //         authFlowType: 'USER_AUTH',
  //         preferredChallenge: 'EMAIL_OTP',
  //       },
  //     });
  //     if (nextStep.signInStep === 'CONFIRM_SIGN_IN_WITH_EMAIL_CODE') {
  //       setAuthFlow('SIGNIN');
  //       setShowOtpVerification(true);
  //       toast.success('OTP Sent', {
  //         description: 'Verification code sent to your email',
  //       });
  //     } else {
  //       toast.error('Unhandled step', {
  //         description: nextStep.signInStep,
  //       });
  //     }
  //   } catch (error) {
  //     toast.error((error as Error).name, {
  //       description: (error as Error).message,
  //     });
  //   }
  // };

  // Try signup if login fails with user not found
  const initiateSignup = async (values: z.infer<typeof formSchema>) => {
    try {
      const { nextStep } = await signUp({
        username: values.email,
        options: {
          userAttributes: {
            email: values.email,
          },
          autoSignIn: {
            authFlowType: 'USER_AUTH',
          },
        },
      });
      if (nextStep.signUpStep === 'CONFIRM_SIGN_UP') {
        setAuthFlow('SIGNUP');
        setShowOtpVerification(true);
        toast.success('OTP Sent', {
          description:
            'Confirm your Signup by entering verification code sent to your email',
        });
      } else {
        toast.error('Unhandled step', {
          description: nextStep.signUpStep,
        });
      }
    } catch (error) {
      toast.error((error as Error).name, {
        description: (error as Error).message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // https://docs.amplify.aws/react/build-a-backend/auth/connect-your-frontend/sign-in/
  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);

    // First try to sign in, assuming user already exists
    try {
      const { nextStep } = await signIn({
        username: values.email,
        options: {
          authFlowType: 'USER_AUTH',
          preferredChallenge: 'EMAIL_OTP',
        },
      });

      if (nextStep.signInStep === 'CONFIRM_SIGN_IN_WITH_EMAIL_CODE') {
        setAuthFlow('SIGNIN');
        setShowOtpVerification(true);
        toast.success('OTP Sent', {
          description: 'Verification code sent to your email',
        });
        setIsLoading(false);
      } else {
        toast.error('Unhandled step', {
          description: nextStep.signInStep,
        });
        setIsLoading(false);
      }
    } catch (error) {
      // If user doesn't exist, try to sign up
      if ((error as Error).name === 'UserNotFoundException') {
        void initiateSignup(values);
      } else {
        toast.error((error as Error).name, {
          description: (error as Error).message,
        });
        setIsLoading(false);
      }
    }
  }

  // https://docs.amplify.aws/react/build-a-backend/auth/connect-your-frontend/sign-in/
  const onOtpSubmit = async (otp: string) => {
    const email = form.getValues().email;
    if (authFlow === 'SIGNUP') {
      const { nextStep } = await confirmSignUp({
        username: email,
        confirmationCode: otp,
      });
      if (nextStep.signUpStep === 'COMPLETE_AUTO_SIGN_IN') {
        const response = await autoSignIn();
        if (response.isSignedIn) {
          onOtpVerified();
        }
      }
    } else if (authFlow === 'SIGNIN') {
      const response = await confirmSignIn({
        challengeResponse: otp,
      });
      if (response.isSignedIn) {
        onOtpVerified();
      }
    }
  };

  async function onOtpVerified() {
    try {
      // Fetch user details to determine if admin
      const userDetails = await fetchUserDetails();

      if (userDetails) {
        // Redirect based on user role
        if (userDetails.isAdmin) {
          router.push('/admin/dashboard');
        } else {
          router.push('/dashboard');
        }
      } else {
        // Default redirect if user details couldn't be fetched
        router.push('/dashboard');
      }
    } catch (error) {
      console.error('Error fetching user details:', error);
      // Default redirect on error
      router.push('/dashboard');
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      {showOtpVerification ? (
        <OtpVerification
          onVerified={onOtpVerified}
          email={form.getValues().email}
          authFlow={authFlow}
          onOtpSubmit={onOtpSubmit}
        />
      ) : (
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold">Login</CardTitle>
            <CardDescription>
              Enter your email to receive a verification code
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? 'Sending OTP...' : 'Send OTP'}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
